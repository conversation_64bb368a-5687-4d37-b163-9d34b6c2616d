#pragma once

#include <string>
#include <memory>

namespace arc_common_logger {

/**
 * @brief 日志级别
 */
enum class Level {
    TRACE,
    DEBUG,
    INFO,
    WARN,
    ERROR,
    CRITICAL,
    OFF
};

/**
 * @brief 简洁的异步旋转日志器
 *
 * 特性：
 * - 异步日志记录，高性能
 * - 自动文件轮转
 * - 线程安全
 * - 简洁的接口
 */
class Logger {
public:
    /**
     * @brief 初始化日志器
     * @param log_file 日志文件路径
     * @param max_file_size 最大文件大小（字节），默认10MB
     * @param max_files 最大文件数量，默认5个
     * @param level 日志级别，默认INFO
     * @return true 成功，false 失败
     */
    static bool init(const std::string& log_file,
                    size_t max_file_size = 10 * 1024 * 1024,
                    size_t max_files = 5,
                    Level level = Level::INFO);

    /**
     * @brief 关闭日志器
     */
    static void shutdown();

    /**
     * @brief 设置日志级别
     */
    static void set_level(Level level);

    /**
     * @brief 刷新日志缓冲区
     */
    static void flush();

    /**
     * @brief 手动触发文件轮转
     */
    static void rotate();

    // 基本日志记录
    static void trace(const std::string& msg);
    static void debug(const std::string& msg);
    static void info(const std::string& msg);
    static void warn(const std::string& msg);
    static void error(const std::string& msg);
    static void critical(const std::string& msg);

    // 格式化日志记录 - 使用内部辅助函数
    template<typename... Args>
    static void trace(const std::string& fmt, Args&&... args) {
        trace_impl(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    static void debug(const std::string& fmt, Args&&... args) {
        debug_impl(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    static void info(const std::string& fmt, Args&&... args) {
        info_impl(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    static void warn(const std::string& fmt, Args&&... args) {
        warn_impl(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    static void error(const std::string& fmt, Args&&... args) {
        error_impl(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    static void critical(const std::string& fmt, Args&&... args) {
        critical_impl(fmt, std::forward<Args>(args)...);
    }



private:
    // 内部辅助函数声明
    template<typename... Args>
    static void trace_impl(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void debug_impl(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void info_impl(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void warn_impl(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void error_impl(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void critical_impl(const std::string& fmt, Args&&... args);

    class Impl;
    static std::unique_ptr<Impl> impl_;
};

} // namespace arc_common_logger

// 全局便利函数
#define LOG_TRACE(...)    arc_common_logger::Logger::trace(__VA_ARGS__)
#define LOG_DEBUG(...)    arc_common_logger::Logger::debug(__VA_ARGS__)
#define LOG_INFO(...)     arc_common_logger::Logger::info(__VA_ARGS__)
#define LOG_WARN(...)     arc_common_logger::Logger::warn(__VA_ARGS__)
#define LOG_ERROR(...)    arc_common_logger::Logger::error(__VA_ARGS__)
#define LOG_CRITICAL(...) arc_common_logger::Logger::critical(__VA_ARGS__)
