#pragma once

#include <string>
#include <memory>

namespace arc_common_logger {

/**
 * @brief 日志级别
 */
enum class Level {
    TRACE,
    DEBUG,
    INFO,
    WARN,
    ERROR,
    CRITICAL,
    OFF
};

/**
 * @brief 简洁的异步旋转日志器
 *
 * 特性：
 * - 异步日志记录，高性能
 * - 自动文件轮转
 * - 线程安全
 * - 简洁的接口
 */
class Logger {
public:
    /**
     * @brief 初始化日志器
     * @param log_file 日志文件路径
     * @param max_file_size 最大文件大小（字节），默认10MB
     * @param max_files 最大文件数量，默认5个
     * @param level 日志级别，默认INFO
     * @return true 成功，false 失败
     */
    static bool init(const std::string& log_file,
                    size_t max_file_size = 10 * 1024 * 1024,
                    size_t max_files = 5,
                    Level level = Level::INFO);

    /**
     * @brief 关闭日志器
     */
    static void shutdown();

    /**
     * @brief 设置日志级别
     */
    static void set_level(Level level);

    /**
     * @brief 刷新日志缓冲区
     */
    static void flush();

    /**
     * @brief 手动触发文件轮转
     */
    static void rotate();

    // 基本日志记录
    static void trace(const std::string& msg);
    static void debug(const std::string& msg);
    static void info(const std::string& msg);
    static void warn(const std::string& msg);
    static void error(const std::string& msg);
    static void critical(const std::string& msg);

    // 格式化日志记录
    template<typename... Args>
    static void trace(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void debug(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void info(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void warn(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void error(const std::string& fmt, Args&&... args);

    template<typename... Args>
    static void critical(const std::string& fmt, Args&&... args);

private:
    class Impl;
    static std::unique_ptr<Impl> impl_;
};

// 模板实现（内联）
template<typename... Args>
void Logger::trace(const std::string& fmt, Args&&... args) {
    if (impl_) {
        // 调用实现类的方法，具体实现在cpp文件中
        impl_->trace_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::debug(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->debug_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::info(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->info_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::warn(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->warn_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::error(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->error_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::critical(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->critical_fmt(fmt, std::forward<Args>(args)...);
    }
}

} // namespace logger_sdk

// 全局便利函数
#define LOG_TRACE(...)    logger_sdk::Logger::trace(__VA_ARGS__)
#define LOG_DEBUG(...)    logger_sdk::Logger::debug(__VA_ARGS__)
#define LOG_INFO(...)     logger_sdk::Logger::info(__VA_ARGS__)
#define LOG_WARN(...)     logger_sdk::Logger::warn(__VA_ARGS__)
#define LOG_ERROR(...)    logger_sdk::Logger::error(__VA_ARGS__)
#define LOG_CRITICAL(...) logger_sdk::Logger::critical(__VA_ARGS__)
