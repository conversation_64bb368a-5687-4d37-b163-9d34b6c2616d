#include "arc_common_logger.h"
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/details/os.h>
#include <mutex>
#include <iostream>

namespace arc_common_logger {

// Logger实现类
class Logger::Impl {
public:
    std::shared_ptr<spdlog::async_logger> async_logger;
    std::mutex mutex;
    bool initialized;

    // 构造函数
    Impl() : initialized(false) {}

    bool init(const std::string& log_file, size_t max_file_size, size_t max_files, Level level) {
        std::lock_guard<std::mutex> lock(mutex);

        if (initialized) {
            return false; // 已经初始化
        }

        try {
            // 创建目录
            create_directories(log_file);

            // 创建异步rotating file sink
            auto rotating_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                log_file, max_file_size, max_files);

            // 创建异步logger
            async_logger = std::make_shared<spdlog::async_logger>(
                "async_rotating_logger",
                rotating_sink,
                spdlog::thread_pool(),
                spdlog::async_overflow_policy::block);

            // 设置日志级别
            async_logger->set_level(to_spdlog_level(level));

            // 设置格式
            async_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v");

            // 注册到spdlog
            spdlog::register_logger(async_logger);

            initialized = true;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "Logger init failed: " << e.what() << std::endl;
            return false;
        }
    }

    void shutdown() {
        std::lock_guard<std::mutex> lock(mutex);
        if (initialized && async_logger) {
            async_logger->flush();
            spdlog::drop("async_rotating_logger");
            async_logger.reset();
            initialized = false;
        }
    }

    void set_level(Level level) {
        std::lock_guard<std::mutex> lock(mutex);
        if (async_logger) {
            async_logger->set_level(to_spdlog_level(level));
        }
    }

    void flush() {
        std::lock_guard<std::mutex> lock(mutex);
        if (async_logger) {
            async_logger->flush();
        }
    }

    void rotate() {
        std::lock_guard<std::mutex> lock(mutex);
        if (async_logger) {
            auto sinks = async_logger->sinks();
            for (auto& sink : sinks) {
                auto rotating_sink = std::dynamic_pointer_cast<spdlog::sinks::rotating_file_sink_mt>(sink);
                if (rotating_sink) {
                    // rotating_sink->rotate_now();
                    break;
                }
            }
        }
    }

    // 基本日志记录
    void trace(const std::string& msg) {
        if (async_logger) async_logger->trace(msg);
    }

    void debug(const std::string& msg) {
        if (async_logger) async_logger->debug(msg);
    }

    void info(const std::string& msg) {
        if (async_logger) async_logger->info(msg);
    }

    void warn(const std::string& msg) {
        if (async_logger) async_logger->warn(msg);
    }

    void error(const std::string& msg) {
        if (async_logger) async_logger->error(msg);
    }

    void critical(const std::string& msg) {
        if (async_logger) async_logger->critical(msg);
    }

    // 格式化日志记录
    template<typename... Args>
    void trace_fmt(const std::string& fmt, Args&&... args) {
        if (async_logger) async_logger->trace(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void debug_fmt(const std::string& fmt, Args&&... args) {
        if (async_logger) async_logger->debug(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void info_fmt(const std::string& fmt, Args&&... args) {
        if (async_logger) async_logger->info(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void warn_fmt(const std::string& fmt, Args&&... args) {
        if (async_logger) async_logger->warn(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void error_fmt(const std::string& fmt, Args&&... args) {
        if (async_logger) async_logger->error(fmt, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void critical_fmt(const std::string& fmt, Args&&... args) {
        if (async_logger) async_logger->critical(fmt, std::forward<Args>(args)...);
    }

private:
    spdlog::level::level_enum to_spdlog_level(Level level) {
        switch (level) {
            case Level::TRACE:    return spdlog::level::trace;
            case Level::DEBUG:    return spdlog::level::debug;
            case Level::INFO:     return spdlog::level::info;
            case Level::WARN:     return spdlog::level::warn;
            case Level::ERROR:    return spdlog::level::err;
            case Level::CRITICAL: return spdlog::level::critical;
            case Level::OFF:      return spdlog::level::off;
            default:              return spdlog::level::info;
        }
    }

    void create_directories(const std::string& file_path) {
        auto dir = spdlog::details::os::dir_name(file_path);
        if (!dir.empty()) {
            spdlog::details::os::create_dir(dir);
        }
    }
};

// 静态成员定义
std::unique_ptr<Logger::Impl> Logger::impl_ = nullptr;

// Logger静态方法实现
bool Logger::init(const std::string& log_file, size_t max_file_size, size_t max_files, Level level) {
    if (!impl_) {
        impl_.reset(new Impl());
    }
    return impl_->init(log_file, max_file_size, max_files, level);
}

void Logger::shutdown() {
    if (impl_) {
        impl_->shutdown();
        impl_.reset();
    }
}

void Logger::set_level(Level level) {
    if (impl_) {
        impl_->set_level(level);
    }
}

void Logger::flush() {
    if (impl_) {
        impl_->flush();
    }
}

void Logger::rotate() {
    if (impl_) {
        impl_->rotate();
    }
}

// 基本日志记录实现
void Logger::trace(const std::string& msg) {
    if (impl_) impl_->trace(msg);
}

void Logger::debug(const std::string& msg) {
    if (impl_) impl_->debug(msg);
}

void Logger::info(const std::string& msg) {
    if (impl_) impl_->info(msg);
}

void Logger::warn(const std::string& msg) {
    if (impl_) impl_->warn(msg);
}

void Logger::error(const std::string& msg) {
    if (impl_) impl_->error(msg);
}

void Logger::critical(const std::string& msg) {
    if (impl_) impl_->critical(msg);
}



// 内部辅助函数实现
template<typename... Args>
void Logger::trace_impl(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->trace_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::debug_impl(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->debug_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::info_impl(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->info_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::warn_impl(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->warn_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::error_impl(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->error_fmt(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void Logger::critical_impl(const std::string& fmt, Args&&... args) {
    if (impl_) {
        impl_->critical_fmt(fmt, std::forward<Args>(args)...);
    }
}

// 显式实例化常用的模板特化
template void Logger::trace_impl<const char*>(const std::string&, const char*&&);
template void Logger::debug_impl<const char*>(const std::string&, const char*&&);
template void Logger::info_impl<const char*>(const std::string&, const char*&&);
template void Logger::warn_impl<const char*>(const std::string&, const char*&&);
template void Logger::error_impl<const char*>(const std::string&, const char*&&);
template void Logger::critical_impl<const char*>(const std::string&, const char*&&);

template void Logger::trace_impl<int>(const std::string&, int&&);
template void Logger::debug_impl<int>(const std::string&, int&&);
template void Logger::info_impl<int>(const std::string&, int&&);
template void Logger::warn_impl<int>(const std::string&, int&&);
template void Logger::error_impl<int>(const std::string&, int&&);
template void Logger::critical_impl<int>(const std::string&, int&&);

template void Logger::trace_impl<const char*, int>(const std::string&, const char*&&, int&&);
template void Logger::debug_impl<const char*, int>(const std::string&, const char*&&, int&&);
template void Logger::info_impl<const char*, int>(const std::string&, const char*&&, int&&);
template void Logger::warn_impl<const char*, int>(const std::string&, const char*&&, int&&);
template void Logger::error_impl<const char*, int>(const std::string&, const char*&&, int&&);
template void Logger::critical_impl<const char*, int>(const std::string&, const char*&&, int&&);

} // namespace arc_common_logger
