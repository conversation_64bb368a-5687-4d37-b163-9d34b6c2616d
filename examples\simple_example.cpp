#include "arc_common_logger.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    // Initialize the logger
    if (!arc_common_logger::Logger::init("logs/example.log", 1024*1024, 3, arc_common_logger::Level::DEBUG)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return 1;
    }

    std::cout << "Logger initialized successfully!" << std::endl;

    // Test basic logging
    arc_common_logger::Logger::info("Application started");
    arc_common_logger::Logger::debug("This is a debug message");
    arc_common_logger::Logger::warn("This is a warning message");
    arc_common_logger::Logger::error("This is an error message");

    // Test formatted logging
    arc_common_logger::Logger::info("User {} logged in with ID {}", "john_doe", 12345);
    arc_common_logger::Logger::debug("Processing {} items in {} seconds", 100, 2.5);

    // Test convenience macros
    LOG_INFO("Using convenience macro");
    LOG_DEBUG("Debug message via macro with value: {}", 42);
    LOG_WARN("Warning via macro");

    // Simulate some work
    for (int i = 0; i < 5; ++i) {
        LOG_INFO("Processing item {}", i + 1);
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Test level changes
    arc_common_logger::Logger::set_level(arc_common_logger::Level::WARN);
    LOG_DEBUG("This debug message should not appear");
    LOG_INFO("This info message should not appear");
    LOG_WARN("This warning should appear");

    // Flush and shutdown
    arc_common_logger::Logger::flush();
    arc_common_logger::Logger::shutdown();

    std::cout << "Example completed successfully!" << std::endl;
    return 0;
}
