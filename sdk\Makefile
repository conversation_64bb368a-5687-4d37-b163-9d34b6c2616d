# Makefile for spdlog_utils project
# A C++ logging utility wrapper around spdlog

# Project configuration
PROJECT_NAME = arc_common_logger
VERSION = 1.0.0

# Directories
SRC_DIR = src
INC_DIR = inc
BUILD_DIR = ../build
OBJ_DIR = $(BUILD_DIR)/obj
LIB_DIR = $(BUILD_DIR)/lib
BIN_DIR = $(BUILD_DIR)/bin
EXAMPLES_DIR = ../examples
INSTALL_PREFIX = $(BUILD_DIR)/install

# spdlog configuration
SPDLOG_ROOT = ../Related_Project/spdlog
SPDLOG_INC = $(SPDLOG_ROOT)/include
SPDLOG_LIB = $(SPDLOG_ROOT)/lib

# Compiler configuration
CROSS_COMPILE = aarch64-linux-gnu-
CC = $(CROSS_COMPILE)gcc
CXX = $(CROSS_COMPILE)g++
STRIP = $(CROSS_COMPILE)strip
AR = $(CROSS_COMPILE)ar
LD = $(CROSS_COMPILE)ld
CXXFLAGS = -std=gnu++11 -Wall -Wextra -O2 -fPIC
DEBUG_FLAGS = -g -DDEBUG -O0
RELEASE_FLAGS = -DNDEBUG -O3

# Include paths
INCLUDES = -I$(INC_DIR) -I$(SPDLOG_INC)

# Library paths and linking
LDFLAGS = -L$(SPDLOG_LIB)
LIBS = -lspdlog -lpthread

# Source files
SOURCES = $(wildcard $(SRC_DIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRC_DIR)/%.cpp=$(OBJ_DIR)/%.o)
HEADERS = $(wildcard $(INC_DIR)/*.h)

# Target libraries
STATIC_LIB = $(LIB_DIR)/lib$(PROJECT_NAME).a
SHARED_LIB = $(LIB_DIR)/lib$(PROJECT_NAME).so
SHARED_LIB_VERSIONED = $(SHARED_LIB).$(VERSION)

# Example sources (if any exist)
EXAMPLE_SOURCES = $(wildcard $(EXAMPLES_DIR)/*.cpp)
EXAMPLE_TARGETS = $(EXAMPLE_SOURCES:$(EXAMPLES_DIR)/%.cpp=$(BIN_DIR)/%)

# Default target
.PHONY: all
all: static shared

# Create directories
$(BUILD_DIR) $(OBJ_DIR) $(LIB_DIR) $(BIN_DIR):
	@mkdir -p $@

# Compile object files
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp $(HEADERS) | $(OBJ_DIR)
	@echo "Compiling $<..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Build static library
.PHONY: static
static: $(STATIC_LIB)

$(STATIC_LIB): $(OBJECTS) | $(LIB_DIR)
	@echo "Creating static library $@..."
	ar rcs $@ $^
	@echo "Static library created: $@"

# Build shared library
.PHONY: shared
shared: $(SHARED_LIB)

$(SHARED_LIB): $(OBJECTS) | $(LIB_DIR)
	@echo "Creating shared library $@..."
	$(CXX) -shared -Wl,-soname,lib$(PROJECT_NAME).so.1 -o $(SHARED_LIB_VERSIONED) $^ $(LDFLAGS) $(LIBS)
	cd $(LIB_DIR) && ln -sf lib$(PROJECT_NAME).so.$(VERSION) lib$(PROJECT_NAME).so.1
	cd $(LIB_DIR) && ln -sf lib$(PROJECT_NAME).so.1 lib$(PROJECT_NAME).so
	@echo "Shared library created: $@"

# Build examples
.PHONY: examples
examples: $(EXAMPLE_TARGETS)

$(BIN_DIR)/%: $(EXAMPLES_DIR)/%.cpp $(STATIC_LIB) | $(BIN_DIR)
	@echo "Building example $@..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) $< -o $@ -L$(LIB_DIR) -l$(PROJECT_NAME) $(LDFLAGS) $(LIBS)

# Debug build
.PHONY: debug
debug: CXXFLAGS += $(DEBUG_FLAGS)
debug: clean all

# Release build
.PHONY: release
release: CXXFLAGS += $(RELEASE_FLAGS)
release: clean all

# Install targets
.PHONY: install
install: all
	@echo "Installing $(PROJECT_NAME)..."
	install -d $(INSTALL_PREFIX)/include/$(PROJECT_NAME)
	install -m 644 $(HEADERS) $(INSTALL_PREFIX)/include/$(PROJECT_NAME)/
	install -d $(INSTALL_PREFIX)/lib
	install -m 644 $(STATIC_LIB) $(INSTALL_PREFIX)/lib/
	install -m 755 $(SHARED_LIB_VERSIONED) $(INSTALL_PREFIX)/lib/
	cd $(INSTALL_PREFIX)/lib && ln -sf lib$(PROJECT_NAME).so.$(VERSION) lib$(PROJECT_NAME).so.1
	cd $(INSTALL_PREFIX)/lib && ln -sf lib$(PROJECT_NAME).so.1 lib$(PROJECT_NAME).so
	@echo "Installation completed."

.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(PROJECT_NAME)..."
	rm -rf $(INSTALL_PREFIX)/include/$(PROJECT_NAME)
	rm -f $(INSTALL_PREFIX)/lib/lib$(PROJECT_NAME).*
	@echo "Uninstallation completed."

# Package configuration
.PHONY: pkgconfig
pkgconfig: $(BUILD_DIR)/$(PROJECT_NAME).pc

$(BUILD_DIR)/$(PROJECT_NAME).pc: | $(BUILD_DIR)
	@echo "Generating pkg-config file..."
	@echo "prefix=$(INSTALL_PREFIX)" > $@
	@echo "exec_prefix=\$${prefix}" >> $@
	@echo "libdir=\$${exec_prefix}/lib" >> $@
	@echo "includedir=\$${prefix}/include" >> $@
	@echo "" >> $@
	@echo "Name: $(PROJECT_NAME)" >> $@
	@echo "Description: Arc Common Logger - A C++ logging utility wrapper around spdlog" >> $@
	@echo "Version: $(VERSION)" >> $@
	@echo "Requires: spdlog" >> $@
	@echo "Libs: -L\$${libdir} -l$(PROJECT_NAME)" >> $@
	@echo "Cflags: -I\$${includedir}/$(PROJECT_NAME)" >> $@

# Testing (placeholder for when tests are added)
.PHONY: test
test:
	@echo "No tests configured yet. Add test files to run tests."

# Clean targets
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	@echo "Clean completed."

.PHONY: distclean
distclean: clean
	@echo "Performing deep clean..."
	find . -name "*.o" -delete
	find . -name "*.a" -delete
	find . -name "*.so*" -delete
	find . -name "*~" -delete
	@echo "Deep clean completed."

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all        - Build both static and shared libraries (default)"
	@echo "  static     - Build static library only"
	@echo "  shared     - Build shared library only"
	@echo "  examples   - Build example programs"
	@echo "  debug      - Build with debug flags"
	@echo "  release    - Build with release optimization"
	@echo "  install    - Install libraries and headers"
	@echo "  uninstall  - Remove installed files"
	@echo "  pkgconfig  - Generate pkg-config file"
	@echo "  test       - Run tests (when available)"
	@echo "  clean      - Remove build artifacts"
	@echo "  distclean  - Remove all generated files"
	@echo "  help       - Show this help message"
	@echo ""
	@echo "Variables you can override:"
	@echo "  CXX           - C++ compiler (default: g++)"
	@echo "  CXXFLAGS      - Compiler flags"
	@echo "  INSTALL_PREFIX - Installation prefix (default: /usr/local)"
	@echo ""
	@echo "Example usage:"
	@echo "  make                    # Build libraries"
	@echo "  make debug              # Build with debug info"
	@echo "  make CXX=clang++        # Use clang++ compiler"
	@echo "  make INSTALL_PREFIX=~/local install  # Install to custom location"

# Show project information
.PHONY: info
info:
	@echo "Project: $(PROJECT_NAME) v$(VERSION)"
	@echo "Source directory: $(SRC_DIR)"
	@echo "Include directory: $(INC_DIR)"
	@echo "Build directory: $(BUILD_DIR)"
	@echo "spdlog location: $(SPDLOG_ROOT)"
	@echo "Compiler: $(CXX)"
	@echo "Compiler flags: $(CXXFLAGS)"
	@echo "Sources found: $(words $(SOURCES)) files"
	@echo "Headers found: $(words $(HEADERS)) files"

# Dependency checking
.PHONY: check-deps
check-deps:
	@echo "Checking dependencies..."
	@if exist "$(SPDLOG_ROOT)" ( \
		echo "spdlog found at $(SPDLOG_ROOT)" \
	) else ( \
		echo "ERROR: spdlog not found at $(SPDLOG_ROOT)" && \
		exit 1 \
	)
	@if exist "$(SPDLOG_LIB)\libspdlog.so" ( \
		echo "spdlog shared library found" \
	) else ( \
		echo "WARNING: spdlog shared library not found at $(SPDLOG_LIB)/libspdlog.so" && \
		echo "Make sure spdlog is properly built and installed." \
	)
	@echo "Dependencies check completed."

# Force rebuild
.PHONY: rebuild
rebuild: clean all

# Print variables (for debugging Makefile)
.PHONY: print-vars
print-vars:
	@echo "SOURCES = $(SOURCES)"
	@echo "OBJECTS = $(OBJECTS)"
	@echo "HEADERS = $(HEADERS)"
	@echo "INCLUDES = $(INCLUDES)"
	@echo "LDFLAGS = $(LDFLAGS)"
	@echo "LIBS = $(LIBS)"
