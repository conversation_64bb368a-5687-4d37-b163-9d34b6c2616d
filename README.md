# Arc Common Logger

A C++ logging utility wrapper around spdlog providing a simple, high-performance asynchronous logging interface.

## Features

- **Asynchronous logging** for high performance
- **Automatic file rotation** with configurable size and count limits
- **Thread-safe** operations
- **Simple API** with both basic and formatted logging
- **Convenience macros** for easy usage
- **Configurable log levels**

## Building

### Prerequisites

- C++14 compatible compiler (GCC 5.4+, Clang 3.8+, MSVC 2017+)
- spdlog library (included in `Related_Project/spdlog`)
- pthread library (usually available on Unix-like systems)

### Quick Start

```bash
# Build both static and shared libraries
make

# Build with debug information
make debug

# Build with release optimizations
make release

# Build only static library
make static

# Build only shared library
make shared

# Build example programs
make examples

# Install to system directories (requires sudo)
sudo make install

# Install to custom location
make INSTALL_PREFIX=~/local install
```

### Available Make Targets

| Target | Description |
|--------|-------------|
| `all` | Build both static and shared libraries (default) |
| `static` | Build static library only |
| `shared` | Build shared library only |
| `examples` | Build example programs |
| `debug` | Build with debug flags |
| `release` | Build with release optimization |
| `install` | Install libraries and headers |
| `uninstall` | Remove installed files |
| `pkgconfig` | Generate pkg-config file |
| `test` | Run tests (when available) |
| `clean` | Remove build artifacts |
| `distclean` | Remove all generated files |
| `help` | Show help message |
| `info` | Show project information |
| `check-deps` | Check dependencies |
| `rebuild` | Clean and build |
| `print-vars` | Print Makefile variables (for debugging) |

### Customization

You can override various settings:

```bash
# Use different compiler
make CXX=clang++

# Add custom flags
make CXXFLAGS="-std=c++17 -O3 -march=native"

# Install to custom prefix
make INSTALL_PREFIX=/opt/arc_logger install

# Use different spdlog location
make SPDLOG_ROOT=/usr/local/spdlog
```

## Usage

### Basic Example

```cpp
#include "arc_common_logger.h"

int main() {
    // Initialize logger
    if (!arc_common_logger::Logger::init("app.log")) {
        return 1;
    }

    // Basic logging
    arc_common_logger::Logger::info("Application started");
    arc_common_logger::Logger::error("Something went wrong");

    // Formatted logging
    arc_common_logger::Logger::info("User {} has {} points", "alice", 1500);

    // Using convenience macros
    LOG_INFO("This is easier to use");
    LOG_DEBUG("Debug value: {}", 42);

    // Cleanup
    arc_common_logger::Logger::shutdown();
    return 0;
}
```

### Compilation

After building the library, compile your application:

```bash
# Using static library
g++ -std=c++14 -I./sdk/inc your_app.cpp -L./build/lib -larc_common_logger -lspdlog -lpthread

# Using shared library
g++ -std=c++14 -I./sdk/inc your_app.cpp -L./build/lib -larc_common_logger -lspdlog -lpthread

# If installed system-wide
g++ -std=c++14 your_app.cpp -larc_common_logger -lspdlog -lpthread
```

### Using pkg-config

After running `make pkgconfig` and installing:

```bash
g++ -std=c++14 your_app.cpp $(pkg-config --cflags --libs arc_common_logger)
```

## API Reference

### Initialization

```cpp
bool Logger::init(const std::string& log_file,
                  size_t max_file_size = 10 * 1024 * 1024,  // 10MB
                  size_t max_files = 5,
                  Level level = Level::INFO);
```

### Log Levels

- `TRACE` - Most verbose
- `DEBUG` - Debug information
- `INFO` - General information
- `WARN` - Warning messages
- `ERROR` - Error messages
- `CRITICAL` - Critical errors
- `OFF` - Disable logging

### Logging Methods

```cpp
// Basic logging
Logger::trace("message");
Logger::debug("message");
Logger::info("message");
Logger::warn("message");
Logger::error("message");
Logger::critical("message");

// Formatted logging
Logger::info("User {} has {} points", username, points);
Logger::debug("Processing {} items", count);

// Convenience macros
LOG_TRACE("trace message");
LOG_DEBUG("debug message");
LOG_INFO("info message");
LOG_WARN("warning message");
LOG_ERROR("error message");
LOG_CRITICAL("critical message");
```

### Control Methods

```cpp
Logger::set_level(Level::DEBUG);  // Change log level
Logger::flush();                  // Force flush buffers
Logger::rotate();                 // Manually rotate log file
Logger::shutdown();               // Cleanup and close
```

## Project Structure

```
.
├── Makefile              # Main build file
├── README.md            # This file
├── sdk/
│   ├── inc/
│   │   └── arc_common_logger.h    # Header file
│   └── src/
│       └── arc_common_logger.cpp  # Implementation
├── examples/
│   └── simple_example.cpp        # Example usage
├── Related_Project/
│   └── spdlog/                    # spdlog dependency
└── build/                         # Build output (created by make)
    ├── obj/                       # Object files
    ├── lib/                       # Libraries
    └── bin/                       # Example binaries
```

## License

This project uses spdlog which is licensed under the MIT License.
